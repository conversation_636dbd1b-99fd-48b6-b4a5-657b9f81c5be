@extends('layouts.app')

@section('content')
<div class="m-5">
    <!-- HEADER -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">DASHBOARD</h1>
            <p class="text-gray-500">Welcome to your dashboard</p>
        </div>

        <div>
            <button class="bg-blue-600 text-white text-sm font-bold py-2 px-5">
                <i class="fas fa-download mr-2"></i>
                Download Reports
            </button>
        </div>
    </div>

    <!-- GRID & CHARTS -->
    <div class="grid grid-cols-12 gap-5 mt-5">
        <!-- ROW 1 -->
        <div class="col-span-3 bg-white dark:bg-gray-800 flex items-center justify-center p-4 rounded shadow">
            <x-stat-box 
                title="12,361"
                subtitle="Emails Sent"
                progress="0.75"
                increase="+14%"
                icon="fas fa-envelope"
                iconColor="text-green-500"
            />
        </div>
        <div class="col-span-3 bg-white dark:bg-gray-800 flex items-center justify-center p-4 rounded shadow">
            <x-stat-box 
                title="431,225"
                subtitle="Sales Obtained"
                progress="0.50"
                increase="+21%"
                icon="fas fa-cash-register"
                iconColor="text-green-500"
            />
        </div>
        <div class="col-span-3 bg-white dark:bg-gray-800 flex items-center justify-center p-4 rounded shadow">
            <x-stat-box 
                title="32,441"
                subtitle="New Clients"
                progress="0.30"
                increase="+5%"
                icon="fas fa-user-plus"
                iconColor="text-green-500"
            />
        </div>
        <div class="col-span-3 bg-white dark:bg-gray-800 flex items-center justify-center p-4 rounded shadow">
            <x-stat-box 
                title="1,325,134"
                subtitle="Traffic Received"
                progress="0.80"
                increase="+43%"
                icon="fas fa-traffic-light"
                iconColor="text-green-500"
            />
        </div>

        <!-- ROW 2 -->
        <div class="col-span-8 row-span-2 bg-white dark:bg-gray-800 rounded shadow">
            <div class="mt-6 px-7 flex justify-between items-center">
                <div>
                    <h5 class="font-semibold text-gray-700 dark:text-gray-200">Revenue Generated</h5>
                    <h3 class="text-2xl font-bold text-green-500">${{ number_format($revenueGenerated, 2) }}</h3>
                </div>
                <div>
                    <button class="text-green-500">
                        <i class="fas fa-download text-2xl"></i>
                    </button>
                </div>
            </div>
            <div class="h-64 -mt-5">
                <div id="lineChart" style="height: 250px;"></div>
            </div>
        </div>

        <div class="col-span-4 row-span-2 bg-white dark:bg-gray-800 rounded shadow overflow-auto">
            <div class="flex justify-between items-center border-b-4 border-gray-700 dark:border-gray-600 p-4">
                <h5 class="font-semibold text-gray-700 dark:text-gray-200">Recent Transactions</h5>
            </div>
            @foreach($transactions as $transaction)
            <div class="flex justify-between items-center border-b-4 border-gray-700 dark:border-gray-600 p-4">
                <div>
                    <h5 class="font-semibold text-green-500">{{ $transaction->txId }}</h5>
                    <p class="text-gray-700 dark:text-gray-200">{{ $transaction->user }}</p>
                </div>
                <div class="text-gray-700 dark:text-gray-200">{{ $transaction->date }}</div>
                <div class="bg-green-500 px-2 py-1 rounded text-white">${{ $transaction->cost }}</div>
            </div>
            @endforeach
        </div>

        <!-- ROW 3 -->
        <div class="col-span-4 row-span-2 bg-white dark:bg-gray-800 rounded shadow p-7">
            <h5 class="font-semibold text-gray-700 dark:text-gray-200">Campaign</h5>
            <div class="flex flex-col items-center mt-6">
                <div id="progressCircle" class="h-32 w-32"></div>
                <h5 class="font-semibold text-green-500 mt-4">${{ number_format($campaignRevenue, 0) }} revenue generated</h5>
                <p class="text-center">Includes extra misc expenditures and costs</p>
            </div>
        </div>

        <div class="col-span-4 row-span-2 bg-white dark:bg-gray-800 rounded shadow">
            <h5 class="font-semibold text-gray-700 dark:text-gray-200 p-7 pb-0">Sales Quantity</h5>
            <div class="h-64 -mt-5">
                <div id="barChart" style="height: 250px;"></div>
            </div>
        </div>

        <div class="col-span-4 row-span-2 bg-white dark:bg-gray-800 rounded shadow p-7">
            <h5 class="font-semibold text-gray-700 dark:text-gray-200 mb-4">Geography Based Traffic</h5>
            <div class="h-52">
                <div id="geographyChart" style="height: 200px;"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Line Chart
        const lineChartOptions = {
            chart: {
                type: 'line',
                height: 250,
                toolbar: {
                    show: false
                }
            },
            series: [{
                name: 'Revenue',
                data: @json($lineChartData)
            }],
            colors: ['#4caf50'],
            stroke: {
                curve: 'smooth',
                width: 3
            },
            xaxis: {
                categories: @json($lineChartLabels)
            }
        };
        new ApexCharts(document.querySelector("#lineChart"), lineChartOptions).render();
        
        // Bar Chart
        const barChartOptions = {
            chart: {
                type: 'bar',
                height: 250,
                toolbar: {
                    show: false
                }
            },
            series: [{
                name: 'Sales',
                data: @json($barChartData)
            }],
            colors: ['#4caf50'],
            xaxis: {
                categories: @json($barChartLabels)
            }
        };
        new ApexCharts(document.querySelector("#barChart"), barChartOptions).render();
        
        // Progress Circle
        const progressCircleOptions = {
            chart: {
                height: 125,
                type: 'radialBar',
            },
            series: [{{ $campaignProgress * 100 }}],
            colors: ['#4caf50'],
            plotOptions: {
                radialBar: {
                    hollow: {
                        size: '60%'
                    }
                }
            }
        };
        new ApexCharts(document.querySelector("#progressCircle"), progressCircleOptions).render();
        
        // Geography Chart
        const geographyMapData = @json($geographyMapData);
        const map = new jsVectorMap({
            selector: '#geographyChart',
            map: 'world',
            series: {
                regions: [{
                    values: geographyMapData,
                    scale: ['#deebf7', '#3182bd'],
                }]
            }
        });
    });
</script>
@endpush