namespace App\Http\Controllers;

use Illuminate\Http\Request;

class BudgetController extends Controller
{
    public function index()
    {
        return view('budget.index'); // Pastikan Anda membuat view ini
    }

    public function store(Request $request)
    {
        // Validasi input
        $request->validate([
            'project_name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
        ]);

        // Simpan data anggaran ke database (misalnya menggunakan model Budget)
        // Budget::create($request->all());

        // Redirect kembali ke halaman budget dengan pesan sukses
        return redirect()->route('budget.index')->with('success', 'Anggaran berhasil ditambahkan!');
    }
} 