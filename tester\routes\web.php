<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ThemeController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\BudgetController;
use App\Http\Controllers\CalendarController;
use App\Http\Controllers\ReportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard routes
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/projects', [ProjectController::class, 'index'])->name('projects.index');
Route::get('/teams', [TeamController::class, 'index'])->name('teams.index');
Route::get('/budget', [BudgetController::class, 'index'])->name('budget.index');
Route::get('/calendar', [CalendarController::class, 'index'])->name('calendar.index');
Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');

// Theme toggle
Route::post('/toggle-theme', [ThemeController::class, 'toggle'])->name('toggle.theme');

// Placeholder routes for sidebar links
Route::get('/team', function () { return view('placeholder', ['title' => 'Team Management']); })->name('team');
Route::get('/contacts', function () { return view('placeholder', ['title' => 'Contacts']); })->name('contacts');
Route::get('/invoices', function () { return view('placeholder', ['title' => 'Invoices']); })->name('invoices');
Route::get('/profiles', function () { return view('placeholder', ['title' => 'Profile']); })->name('profiles');
Route::get('/faq', function () { return view('placeholder', ['title' => 'FAQ']); })->name('faq');
Route::get('/charts', function () { return view('placeholder', ['title' => 'Charts']); })->name('charts');

Route::post('/reports', [ReportController::class, 'store'])->name('reports.store');
Route::post('/budget', [BudgetController::class, 'store'])->name('budget.store');