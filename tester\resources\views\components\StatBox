<?php

namespace App\View\Components;

use Illuminate\View\Component;

class StatBox extends Component
{
    public $title;
    public $subtitle;
    public $progress;
    public $increase;
    public $icon;
    public $iconColor;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($title, $subtitle, $progress, $increase, $icon, $iconColor)
    {
        $this->title = $title;
        $this->subtitle = $subtitle;
        $this->progress = $progress;
        $this->increase = $increase;
        $this->icon = $icon;
        $this->iconColor = $iconColor;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.stat-box');
    }
}