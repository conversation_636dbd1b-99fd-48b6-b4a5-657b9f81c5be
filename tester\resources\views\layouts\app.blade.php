<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite('resources/css/app.css')

    <title>{{ config('app.name', 'Dashboard') }}</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap">
    
    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- ApexCharts -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@3.35.3/dist/apexcharts.css">
    
    <!-- jsVectorMap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jsvectormap@1.3.3/dist/css/jsvectormap.min.css">
    
    <style>
        :root {
            --color-primary: {{ session('dark_mode', false) ? '#1F2A40' : '#FFFFFF' }};
            --color-secondary: {{ session('dark_mode', false) ? '#141B2D' : '#F8F8F8' }};
            --color-text: {{ session('dark_mode', false) ? '#e0e0e0' : '#333333' }};
            --color-accent: #4cceac;
            --color-accent-secondary: #db4f4a;
            --color-blue-accent: #6870fa;
        }
        
        body {
            font-family: 'Source Sans Pro', sans-serif;
            background-color: var(--color-secondary);
            color: var(--color-text);
        }
        
        .bg-white {
            background-color: var(--color-primary);
        }
        
        .dark-mode-toggle {
            cursor: pointer;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999;
            font-size: 1.5rem;
        }
    </style>

    <!-- Scripts -->
    <script src="{{ asset('js/app.js') }}" defer></script>
</head>
<body>
    <div class="min-h-screen">
        <!-- Dark Mode Toggle -->
        <div class="dark-mode-toggle">
            <i class="fas {{ session('dark_mode', false) ? 'fa-sun' : 'fa-moon' }}"></i>
        </div>
        
        <!-- Sidebar -->
        @include('layouts.sidebar')
        
        <!-- Main Content -->
        <div class="ml-64">
            @yield('content')
        </div>
    </div>
    
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.3/dist/apexcharts.min.js"></script>
    
    <!-- jsVectorMap -->
    <script src="https://cdn.jsdelivr.net/npm/jsvectormap@1.3.3/dist/js/jsvectormap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsvectormap@1.3.3/dist/maps/world.js"></script>
    
    <!-- Dark Mode Toggle -->
    <script>
        document.querySelector('.dark-mode-toggle').addEventListener('click', function() {
            fetch('{{ route("toggle.theme") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            }).then(() => {
                window.location.reload();
            });
        });
    </script>
    
    @stack('scripts')
</body>
</html>