<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ThemeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard routes
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Theme toggle
Route::post('/toggle-theme', [ThemeController::class, 'toggle'])->name('toggle.theme');

// Placeholder routes for sidebar links
Route::get('/team', function () { return view('placeholder', ['title' => 'Team Management']); })->name('team');
Route::get('/contacts', function () { return view('placeholder', ['title' => 'Contacts']); })->name('contacts');
Route::get('/invoices', function () { return view('placeholder', ['title' => 'Invoices']); })->name('invoices');
Route::get('/profiles', function () { return view('placeholder', ['title' => 'Profile']); })->name('profiles');
Route::get('/calendar', function () { return view('placeholder', ['title' => 'Calendar']); })->name('calendar');
Route::get('/faq', function () { return view('placeholder', ['title' => 'FAQ']); })->name('faq');
Route::get('/charts', function () { return view('placeholder', ['title' => 'Charts']); })->name('charts');