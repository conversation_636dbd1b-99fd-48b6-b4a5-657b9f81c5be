<aside class="fixed top-0 left-0 w-64 h-full bg-white dark:bg-gray-800 shadow-lg">
    <div class="p-5">
        <div class="flex items-center mb-8">
            <img src="{{ asset('images/logo.png') }}" alt="Logo" class="w-8 h-8 mr-2">
            <h1 class="text-xl font-bold text-gray-800 dark:text-white">ADMIN</h1>
        </div>
        
        <div class="mb-5">
            <div class="flex items-center">
                <img src="{{ asset('images/user.jpg') }}" alt="User" class="w-16 h-16 rounded-full mr-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white">{{ auth()->user()->name ?? 'Admin User' }}</h2>
                    <p class="text-sm text-gray-500">Admin</p>
                </div>
            </div>
        </div>
        
        <nav>
            <ul>
                <li class="mb-2">
                    <a href="{{ route('dashboard') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'dashboard' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-home mr-2"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="mb-2">
                    <a href="{{ route('team') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'team' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-users mr-2"></i>
                        <span>Manage Team</span>
                    </a>
                </li>
                <li class="mb-2">
                    <a href="{{ route('contacts') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'contacts' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-address-book mr-2"></i>
                        <span>Contacts</span>
                    </a>
                </li>
                <li class="mb-2">
                    <a href="{{ route('invoices') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'invoices' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-file-invoice mr-2"></i>
                        <span>Invoices</span>
                    </a>
                </li>
                <li class="mb-2">
                    <a href="{{ route('profiles') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'profiles' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-id-card mr-2"></i>
                        <span>Profile</span>
                    </a>
                </li>
                <li class="mb-2">
                    <a href="{{ route('calendar') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'calendar' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-calendar mr-2"></i>
                        <span>Calendar</span>
                    </a>
                </li>
                <li class="mb-2">
                    <a href="{{ route('faq') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'faq' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-question-circle mr-2"></i>
                        <span>FAQ</span>
                    </a>
                </li>
                <li class="mb-2">
                    <a href="{{ route('charts') }}" class="flex items-center p-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded {{ Route::currentRouteName() == 'charts' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                        <i class="fas fa-chart-bar mr-2"></i>
                        <span>Charts</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</aside>