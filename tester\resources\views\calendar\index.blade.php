@extends('layouts.app')

@section('content')
<div class="m-5">
    <h1 class="text-3xl font-bold">Calendar Project</h1>
    <div id="calendar"></div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/5.10.1/main.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/5.10.1/main.min.css" rel="stylesheet" />

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var calendarEl = document.getElementById('calendar');
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            events: [
                // Anda bisa menambahkan event di sini
                // { title: 'Event 1', start: '2023-10-01' },
                // { title: 'Event 2', start: '2023-10-05', end: '2023-10-07' }
            ]
        });
        calendar.render();
    });
</script>
@endpush
@endsection 