<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Transaction;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Mock data for the dashboard
        
        // Revenue data
        $revenueGenerated = 59342.32;
        
        // Line chart data
        $lineChartData = [12, 19, 3, 5, 2, 3, 15, 10, 9, 12, 8, 10];
        $lineChartLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        // Campaign data
        $campaignRevenue = 48352;
        $campaignProgress = 0.67; // 67% progress
        
        // Bar chart data
        $barChartData = [65, 59, 80, 81, 56, 55, 40];
        $barChartLabels = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
        
        // Geography map data
        $geographyMapData = [
            'US' => 100,
            'CA' => 80,
            'UK' => 75,
            'AU' => 60,
            'DE' => 50,
            'FR' => 45,
            'IN' => 40,
            'BR' => 35,
        ];
        
        // Mock transactions
        $transactions = collect([
            (object) [
                'txId' => '01e4dsaf',
                'user' => 'johndoe',
                'date' => '2021-09-01',
                'cost' => '43.95'
            ],
            (object) [
                'txId' => '0315dsaa',
                'user' => 'jackdower',
                'date' => '2022-04-01',
                'cost' => '133.45'
            ],
            (object) [
                'txId' => '01e4dsaf',
                'user' => 'aberdohnny',
                'date' => '2021-09-01',
                'cost' => '43.95'
            ],
            (object) [
                'txId' => '51034szv',
                'user' => 'goodmanave',
                'date' => '2022-11-05',
                'cost' => '200.95'
            ],
            (object) [
                'txId' => '0a123sb',
                'user' => 'stevebower',
                'date' => '2022-11-02',
                'cost' => '13.55'
            ],
            (object) [
                'txId' => '01e4dsa',
                'user' => 'aberdohnny',
                'date' => '2021-09-01',
                'cost' => '43.95'
            ],
            (object) [
                'txId' => '120s51a',
                'user' => 'wootzifer',
                'date' => '2019-04-15',
                'cost' => '24.20'
            ],
            (object) [
                'txId' => 'oasif09',
                'user' => 'janesmith',
                'date' => '2023-02-11',
                'cost' => '120.50'
            ]
        ]);

        return view('dashboard', compact(
            'revenueGenerated',
            'lineChartData',
            'lineChartLabels',
            'campaignRevenue',
            'campaignProgress',
            'barChartData',
            'barChartLabels',
            'geographyMapData',
            'transactions'
        ));
    }
}