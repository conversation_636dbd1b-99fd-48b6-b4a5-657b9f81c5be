<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ThemeController extends Controller
{
    /**
     * Toggle between light and dark mode
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function toggle(Request $request)
    {
        // Toggle dark mode in session
        $current = session('dark_mode', false);
        session(['dark_mode' => !$current]);
        
        return response()->json(['success' => true]);
    }
}